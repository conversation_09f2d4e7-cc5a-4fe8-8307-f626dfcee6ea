{"name": "tailadmin-vue-pro-2.0.1", "version": "2.0.1", "private": true, "type": "module", "scripts": {"dev": "vite --mode dev", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/vue3": "^6.1.15", "@heroicons/vue": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "apexcharts": "^4.4.0", "axios": "^1.11.0", "daisyui": "^5.0.47", "dropzone": "^6.0.0-beta.2", "flatpickr": "^4.6.13", "js-cookie": "^3.0.5", "jsvectormap": "^1.6.0", "lodash": "^4.17.21", "lucide-vue-next": "^0.474.0", "nanoid": "^5.1.5", "normalize.css": "^8.0.1", "pinia": "^3.0.3", "qs": "^6.14.0", "swiper": "^11.2.1", "vue": "^3.5.18", "vue-flatpickr-component": "^11.0.5", "vue-kanban": "^1.8.0", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0", "vuedraggable": "^4.1.0", "vuevectormap": "^2.0.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tsconfig/node22": "^22.0.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/node": "^24.1.0", "@types/qs": "^6.14.0", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss": "^8.5.6", "postcss-modules": "^6.0.1", "postcss-pxtorem": "^6.1.0", "prettier": "^3.4.2", "sass-embedded": "^1.83.4", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^7.7.0", "vue-tsc": "^3.0.4"}}