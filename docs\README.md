# 📚 项目文档中心

欢迎来到Go-Vue高性能后台管理系统的文档中心！这里包含了项目的所有技术文档，按前后端分类整理。

## 📁 文档结构

```
docs/
├── README.md                    # 文档索引（本文件）
├── OPTIMIZATION_SUMMARY.md     # 项目整体优化总结
├── backend/                     # 后端文档
│   ├── README.md               # 后端文档索引
│   ├── QPS_OPTIMIZATION_SUMMARY.md    # QPS优化详细方案
│   ├── BUGFIX_SUMMARY.md       # 后端问题修复总结
│   ├── LOGIN_ISSUE_SOLUTION.md # 登录问题解决方案
│   ├── API_DOCUMENTATION.md    # API接口文档
│   ├── DEPLOYMENT_GUIDE.md     # 部署指南
│   └── PERFORMANCE_GUIDE.md    # 性能优化指南
└── frontend/                    # 前端文档
    ├── README.md               # 前端文档索引
    ├── COMPONENT_GUIDE.md      # 组件使用指南
    ├── DEVELOPMENT_GUIDE.md    # 开发指南
    └── BUILD_GUIDE.md          # 构建部署指南
```

## 🚀 快速导航

### 📖 项目概览
- [项目整体优化总结](./OPTIMIZATION_SUMMARY.md) - 完整的项目优化历程和成果

### 🔧 后端文档
- [后端文档索引](./backend/README.md) - 后端所有文档的入口
- [QPS优化方案](./backend/QPS_OPTIMIZATION_SUMMARY.md) - 详细的性能优化实施方案
- [问题修复总结](./backend/BUGFIX_SUMMARY.md) - 后端问题修复记录
- [登录问题解决](./backend/LOGIN_ISSUE_SOLUTION.md) - 登录401错误解决方案

### 🎨 前端文档
- [前端文档索引](./frontend/README.md) - 前端所有文档的入口
- [前端开发指南](./frontend/README.md) - Vue3 + TypeScript开发说明

## 🎯 核心特性文档

### 性能优化
- **QPS提升**: 从200提升至1000+ (5倍提升)
- **响应时间**: 从500ms降至<100ms (5倍提升)
- **错误率**: 控制在<1%以内
- **并发处理**: 支持500+并发连接

### 技术架构
- **后端**: Go + Gin + GORM + MySQL + Redis + RabbitMQ
- **前端**: Vue3 + TypeScript + Element Plus + Vite
- **中间件**: 限流、熔断、缓存、异步日志、性能监控

### 核心组件
- **RabbitMQ消息队列**: 异步处理，削峰填谷
- **Redis缓存优化**: 多层缓存，分布式锁，会话管理
- **数据库连接池**: 高效连接复用，读写分离
- **API限流熔断**: 多算法限流，智能熔断保护
- **异步日志处理**: 高性能日志，不阻塞主业务
- **性能监控**: 实时监控，智能告警

## 📋 文档分类说明

### 🔧 后端文档 (backend/)
包含Go后端相关的所有技术文档：
- 性能优化方案和实施细节
- 问题排查和解决方案
- API接口文档和使用说明
- 部署配置和运维指南
- 中间件集成和配置说明

### 🎨 前端文档 (frontend/)
包含Vue3前端相关的所有技术文档：
- 组件开发和使用指南
- 项目结构和开发规范
- 构建部署和环境配置
- UI/UX设计规范
- 测试和调试指南

## 🛠️ 使用指南

### 开发者
1. 首先阅读对应端的README文档
2. 根据需要查看具体的技术文档
3. 遇到问题时查看问题解决方案文档

### 运维人员
1. 查看部署指南了解系统部署
2. 阅读性能优化指南了解系统调优
3. 参考监控指南设置系统监控

### 项目管理者
1. 查看项目整体优化总结了解项目进展
2. 阅读性能测试报告了解系统性能
3. 参考技术架构文档了解系统设计

## 📞 联系方式

如果您在使用文档过程中遇到问题，请：
1. 首先查看相关的问题解决方案文档
2. 查看项目的Issue列表
3. 联系项目维护者

## 📝 文档维护

- **更新频率**: 随项目开发进度实时更新
- **版本控制**: 所有文档都纳入Git版本控制
- **格式规范**: 使用Markdown格式，遵循统一的文档规范

---

**最后更新**: 2025-07-29  
**文档版本**: v1.0  
**项目版本**: v1.0
