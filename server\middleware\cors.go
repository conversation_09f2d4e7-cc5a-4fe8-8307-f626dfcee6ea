/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-07-24 11:42:37
 * @LastEditors: 很拉风的James
 * @LastEditTime: 2025-07-24 11:47:51
 * @FilePath: /server/middleware/cors.go
 * @Description:
 *
 */
package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		c.Header("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type,AccessToken,X-CSRFToken, Authorization, Token, Origin, Accept")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE, PUT, PATCH, HEAD")

		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		//放行所有OPTIONS方法
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}
		// 处理请求
		c.Next()
	}
}
