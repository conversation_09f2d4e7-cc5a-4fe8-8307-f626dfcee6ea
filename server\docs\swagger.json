{"swagger": "2.0", "info": {"description": "后台管理系统API接口文档", "title": "通用后台管理系统", "contact": {}, "version": "1.0"}, "paths": {"/api/admin/add": {"post": {"security": [{"ApiKeyAuth": []}], "description": "新增用户接口", "produces": ["application/json"], "summary": "新增用户接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.AddSysAdminDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/admin/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "根据id删除接口", "produces": ["application/json"], "summary": "根据id删除接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysAdminIdDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/admin/info": {"get": {"security": [{"ApiKeyAuth": []}], "description": "根据id查询用户接口", "produces": ["application/json"], "summary": "根据id查询用户接口", "parameters": [{"type": "integer", "description": "Id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/admin/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "分页获取用户列表接口", "produces": ["application/json"], "summary": "分页获取用户列表接口", "parameters": [{"type": "integer", "description": "分页数", "name": "pageNum", "in": "query"}, {"type": "integer", "description": "每页数", "name": "pageSize", "in": "query"}, {"type": "string", "description": "用户名", "name": "username", "in": "query"}, {"type": "string", "description": "帐号启用状态：1->启用,2->禁用", "name": "status", "in": "query"}, {"type": "string", "description": "开始时间", "name": "beginTime", "in": "query"}, {"type": "string", "description": "结束时间", "name": "endTime", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/admin/update": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改用户接口", "produces": ["application/json"], "summary": "修改用户接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.UpdateSysAdminDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/admin/updatePassword": {"put": {"security": [{"ApiKeyAuth": []}], "description": "重置密码接口", "produces": ["application/json"], "summary": "重置密码接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.ResetSysAdminPasswordDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/admin/updatePersonal": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改个人信息接口", "produces": ["application/json"], "summary": "修改个人信息接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.UpdatePersonalDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/admin/updatePersonalPassword": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改密码接口", "produces": ["application/json"], "summary": "修改密码接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.UpdatePersonalPasswordDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/admin/updateStatus": {"put": {"security": [{"ApiKeyAuth": []}], "description": "用户状态启用/停用接口", "produces": ["application/json"], "summary": "用户状态启用/停用接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.UpdateSysAdminStatusDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/captcha": {"get": {"description": "验证码接口", "produces": ["application/json"], "summary": "验证码 接口", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/dept/add": {"post": {"security": [{"ApiKeyAuth": []}], "description": "新增部门接口", "produces": ["application/json"], "summary": "新增部门接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysDept"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/dept/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "根据id删除部门接口", "produces": ["application/json"], "summary": "根据id删除部门接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysDeptIdDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/dept/info": {"get": {"security": [{"ApiKeyAuth": []}], "description": "根据id查询部门接口", "produces": ["application/json"], "summary": "根据id查询部门接口", "parameters": [{"type": "integer", "description": "ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/dept/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "查询部门列表接口", "produces": ["application/json"], "summary": "查询部门列表接口", "parameters": [{"type": "string", "description": "部门名称", "name": "deptName", "in": "query"}, {"type": "string", "description": "部门状态", "name": "deptStatus", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/dept/update": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改部门接口", "produces": ["application/json"], "summary": "修改部门接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysDept"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/dept/vo/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "部门下拉列表接口", "produces": ["application/json"], "summary": "部门下拉列表接口", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/login": {"post": {"description": "用户登录接口", "produces": ["application/json"], "summary": "用户登录接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.LoginDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/menu/add": {"post": {"security": [{"ApiKeyAuth": []}], "description": "新增菜单接口", "produces": ["application/json"], "summary": "新增菜单接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysMenu"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/menu/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "根据id删除菜单接口", "produces": ["application/json"], "summary": "根据id删除菜单接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysMenuIdDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/menu/info": {"get": {"security": [{"ApiKeyAuth": []}], "description": "根据id查询菜单", "produces": ["application/json"], "summary": "根据id查询菜单", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/menu/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "查询菜单列表", "produces": ["application/json"], "summary": "查询菜单列表", "parameters": [{"type": "string", "description": "菜单名称", "name": "menuName", "in": "query"}, {"type": "string", "description": "菜单状态", "name": "menuStatus", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/menu/update": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改菜单接口", "produces": ["application/json"], "summary": "修改菜单接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysMenu"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/menu/vo/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "查询新增选项列表接口", "produces": ["application/json"], "summary": "查询新增选项列表接口", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/post/add": {"post": {"security": [{"ApiKeyAuth": []}], "description": "新增岗位接口", "produces": ["application/json"], "summary": "新增岗位接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysPost"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/post/batch/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "批量删除岗位接口", "produces": ["application/json"], "summary": "批量删除岗位接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.DelSysPostDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/post/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "根据id删除岗位接口", "produces": ["application/json"], "summary": "根据id删除岗位接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysPostIdDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/post/info": {"get": {"security": [{"ApiKeyAuth": []}], "description": "根据id查询岗位", "produces": ["application/json"], "summary": "根据id查询岗位", "parameters": [{"type": "integer", "description": "ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/post/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "分页查询岗位列表", "produces": ["application/json"], "summary": "分页查询岗位列表", "parameters": [{"type": "integer", "description": "分页数", "name": "pageNum", "in": "query"}, {"type": "integer", "description": "每页数", "name": "pageSize", "in": "query"}, {"type": "string", "description": "岗位名称", "name": "postName", "in": "query"}, {"type": "string", "description": "状态：1->启用,2->禁用", "name": "postStatus", "in": "query"}, {"type": "string", "description": "开始时间", "name": "beginTime", "in": "query"}, {"type": "string", "description": "结束时间", "name": "endTime", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/post/update": {"put": {"security": [{"ApiKeyAuth": []}], "description": "修改岗位接口", "produces": ["application/json"], "summary": "修改岗位接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysPost"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/post/updateStatus": {"put": {"security": [{"ApiKeyAuth": []}], "description": "岗位状态修改接口", "produces": ["application/json"], "summary": "岗位状态修改接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.UpdateSysPostStatusDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/post/vo/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "岗位下拉列表", "produces": ["application/json"], "summary": "岗位下拉列表", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/add": {"post": {"security": [{"ApikeyAuth": []}], "description": "新增角色接口", "produces": ["application/json"], "summary": "新增角色接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.AddSysRoleDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/assignPermissions": {"put": {"security": [{"ApiKeyAuth": []}], "description": "分配权限接口", "produces": ["application/json"], "summary": "分配权限接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.RoleMenu"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/delete": {"delete": {"security": [{"ApikeyAuth": []}], "description": "根据id删除角色接口", "produces": ["application/json"], "summary": "根据id删除角色接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysRoleIdDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/info": {"get": {"security": [{"ApikeyAuth": []}], "description": "根据id查询角色接口", "produces": ["application/json"], "summary": "根据id查询角色接口", "parameters": [{"type": "integer", "description": "Id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "分页查询角色列表接口", "produces": ["application/json"], "summary": "分页查询角色列表接口", "parameters": [{"type": "integer", "description": "分页数", "name": "pageNum", "in": "query"}, {"type": "integer", "description": "每页数", "name": "pageSize", "in": "query"}, {"type": "string", "description": "角色名称", "name": "<PERSON><PERSON><PERSON>", "in": "query"}, {"type": "string", "description": "帐号启用状态：1->启用,2->禁用", "name": "status", "in": "query"}, {"type": "string", "description": "开始时间", "name": "beginTime", "in": "query"}, {"type": "string", "description": "结束时间", "name": "endTime", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/update": {"put": {"security": [{"ApikeyAuth": []}], "description": "修改角色", "produces": ["application/json"], "summary": "修改角色", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.UpdateSysRoleDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/updateStatus": {"put": {"security": [{"ApikeyAuth": []}], "description": "角色状态启用/停用接口", "produces": ["application/json"], "summary": "角色状态启用/停用接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.UpdateSysRoleStatusDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/vo/idList": {"get": {"security": [{"ApikeyAuth": []}], "description": "根据角色id查询菜单数据接口", "produces": ["application/json"], "summary": "根据角色id查询菜单数据接口", "parameters": [{"type": "integer", "description": "Id", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/role/vo/list": {"get": {"security": [{"ApikeyAuth": []}], "description": "角色下拉列表", "produces": ["application/json"], "summary": "角色下拉列表", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/sysLoginInfo/batch/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "批量删除登录日志接口", "produces": ["application/json"], "summary": "批量删除登录日志接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.DelSysLoginInfoDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/sysLoginInfo/clean": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "清空登录日志接口", "produces": ["application/json"], "summary": "清空登录日志接口", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/sysLoginInfo/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "根据ID删除登录日志接口", "produces": ["application/json"], "summary": "根据ID删除登录日志接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysLoginInfoIdDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/sysLoginInfo/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "分页获取登录日志列表接口", "produces": ["application/json"], "summary": "分页获取登录日志列表接口", "parameters": [{"type": "integer", "description": "分页数", "name": "pageNum", "in": "query"}, {"type": "integer", "description": "每页数", "name": "pageSize", "in": "query"}, {"type": "string", "description": "用户名", "name": "username", "in": "query"}, {"type": "string", "description": "登录状态（1-成功 2-失败）", "name": "loginStatus", "in": "query"}, {"type": "string", "description": "开始时间", "name": "beginTime", "in": "query"}, {"type": "string", "description": "结束时间", "name": "endTime", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/sysOperationLog/batch/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "批量删除操作日志接口", "produces": ["application/json"], "summary": "批量删除操作日志接口", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.BatchDeleteSysOperationLogDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/sysOperationLog/clean": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "清空操作日志接口", "produces": ["application/json"], "summary": "清空操作日志接口", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/sysOperationLog/delete": {"delete": {"security": [{"ApiKeyAuth": []}], "description": "根据id删除操作日志", "produces": ["application/json"], "summary": "根据id删除操作日志", "parameters": [{"description": "data", "name": "data", "in": "body", "required": true, "schema": {"$ref": "#/definitions/entity.SysOperationLogIdDto"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/sysOperationLog/list": {"get": {"security": [{"ApiKeyAuth": []}], "description": "分页获取操作日志列表接口", "produces": ["application/json"], "summary": "分页获取操作日志列表接口", "parameters": [{"type": "integer", "description": "每页数", "name": "pageSize", "in": "query"}, {"type": "integer", "description": "分页数", "name": "pageNum", "in": "query"}, {"type": "string", "description": "用户名", "name": "username", "in": "query"}, {"type": "string", "description": "开始时间", "name": "beginTime", "in": "query"}, {"type": "string", "description": "结束时间", "name": "endTime", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/api/upload": {"post": {"security": [{"ApiKeyAuth": []}], "description": "单图片上传接口", "consumes": ["multipart/form-data"], "produces": ["application/json"], "summary": "单图片上传接口", "parameters": [{"type": "file", "description": "file", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/result.Result"}}}}}}, "definitions": {"entity.AddSysAdminDto": {"type": "object", "required": ["deptId", "email", "nickname", "password", "phone", "postId", "roleId", "status", "username"], "properties": {"deptId": {"description": "部门id", "type": "integer"}, "email": {"description": "邮箱", "type": "string"}, "nickname": {"description": "昵称", "type": "string"}, "note": {"description": "备注", "type": "string"}, "password": {"description": "密码", "type": "string"}, "phone": {"description": "手机号", "type": "string"}, "postId": {"description": "岗位id", "type": "integer"}, "roleId": {"description": "角色id", "type": "integer"}, "status": {"description": "状态：1->启用,2->禁用", "type": "integer"}, "username": {"description": "用户名", "type": "string"}}}, "entity.AddSysRoleDto": {"type": "object", "properties": {"description": {"description": "描述", "type": "string"}, "roleKey": {"description": "角色key", "type": "string"}, "roleName": {"description": "角色名称", "type": "string"}, "status": {"description": "状态：1->启用,2->禁用", "type": "integer"}}}, "entity.BatchDeleteSysOperationLogDto": {"type": "object", "properties": {"ids": {"description": "id列表", "type": "array", "items": {"type": "integer"}}}}, "entity.DelSysLoginInfoDto": {"type": "object", "properties": {"ids": {"description": "Id列表", "type": "array", "items": {"type": "integer"}}}}, "entity.DelSysPostDto": {"type": "object", "properties": {"ids": {"description": "Id列表", "type": "array", "items": {"type": "integer"}}}}, "entity.LoginDto": {"type": "object", "required": ["id<PERSON><PERSON>", "image", "password", "username"], "properties": {"idKey": {"description": "uuid", "type": "string"}, "image": {"description": "验证码", "type": "string", "maxLength": 6, "minLength": 4}, "password": {"description": "密码", "type": "string"}, "username": {"description": "用户名", "type": "string"}}}, "entity.ResetSysAdminPasswordDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}, "password": {"description": "密码", "type": "string"}}}, "entity.RoleMenu": {"type": "object", "required": ["id", "menuIds"], "properties": {"id": {"description": "ID", "type": "integer"}, "menuIds": {"description": "菜单id列表", "type": "array", "items": {"type": "integer"}}}}, "entity.SysAdminIdDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}}}, "entity.SysDept": {"type": "object", "properties": {"children": {"description": "子集", "type": "array", "items": {"$ref": "#/definitions/entity.SysDept"}}, "createTime": {"description": "创建时间", "allOf": [{"$ref": "#/definitions/utils.HTime"}]}, "deptName": {"description": "部门名称", "type": "string"}, "deptStatus": {"description": "部门状态（1->正常 2->停用）", "type": "integer"}, "deptType": {"description": "部门类型（1->公司, 2->中心，3->部门）", "type": "integer"}, "id": {"description": "ID", "type": "integer"}, "parentId": {"description": "父id", "type": "integer"}}}, "entity.SysDeptIdDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}}}, "entity.SysLoginInfoIdDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}}}, "entity.SysMenu": {"type": "object", "properties": {"children": {"description": "子集", "type": "array", "items": {"$ref": "#/definitions/entity.SysMenu"}}, "createTime": {"description": "创建时间", "allOf": [{"$ref": "#/definitions/utils.HTime"}]}, "icon": {"description": "菜单图标", "type": "string"}, "id": {"description": "ID", "type": "integer"}, "menuName": {"description": "菜单名称", "type": "string"}, "menuStatus": {"description": "启用状态；1->禁用；2->启用", "type": "integer"}, "menuType": {"description": "菜单类型：1->目录；2->菜单；3->按钮", "type": "integer"}, "parentId": {"description": "父菜单id", "type": "integer"}, "sort": {"description": "排序", "type": "integer"}, "url": {"description": "菜单url", "type": "string"}, "value": {"description": "权限值", "type": "string"}}}, "entity.SysMenuIdDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}}}, "entity.SysOperationLogIdDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}}}, "entity.SysPost": {"type": "object", "properties": {"createTime": {"description": "创建时间", "allOf": [{"$ref": "#/definitions/utils.HTime"}]}, "id": {"description": "ID", "type": "integer"}, "postCode": {"description": "岗位编码", "type": "string"}, "postName": {"description": "岗位名称", "type": "string"}, "postStatus": {"description": "状态（1->正常 2->停用）", "type": "integer"}, "remark": {"description": "备注", "type": "string"}}}, "entity.SysPostIdDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}}}, "entity.SysRoleIdDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}}}, "entity.UpdatePersonalDto": {"type": "object", "required": ["email", "nickname", "note", "phone", "username"], "properties": {"email": {"description": "邮箱", "type": "string"}, "icon": {"description": "头像", "type": "string"}, "id": {"description": "ID", "type": "integer"}, "nickname": {"description": "昵称", "type": "string"}, "note": {"description": "备注", "type": "string"}, "phone": {"description": "电话", "type": "string"}, "username": {"description": "用户名", "type": "string"}}}, "entity.UpdatePersonalPasswordDto": {"type": "object", "required": ["newPassword", "password", "resetPassword"], "properties": {"id": {"description": "ID", "type": "integer"}, "newPassword": {"description": "新密码", "type": "string"}, "password": {"description": "密码", "type": "string"}, "resetPassword": {"description": "重复密码", "type": "string"}}}, "entity.UpdateSysAdminDto": {"type": "object", "properties": {"deptId": {"description": "部门id", "type": "integer"}, "email": {"description": "邮箱", "type": "string"}, "id": {"description": "ID", "type": "integer"}, "nickname": {"description": "昵称", "type": "string"}, "note": {"description": "备注", "type": "string"}, "phone": {"description": "手机号", "type": "string"}, "postId": {"description": "岗位id", "type": "integer"}, "roleId": {"description": "角色id", "type": "integer"}, "status": {"description": "状态：1->启用,2->禁用", "type": "integer"}, "username": {"description": "用户名", "type": "string"}}}, "entity.UpdateSysAdminStatusDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}, "status": {"description": "状态：1->启用,2->禁用", "type": "integer"}}}, "entity.UpdateSysPostStatusDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}, "postStatus": {"description": "状态（1->正常 2->停用）", "type": "integer"}}}, "entity.UpdateSysRoleDto": {"type": "object", "properties": {"description": {"description": "描述", "type": "string"}, "id": {"description": "Id", "type": "integer"}, "roleKey": {"description": "角色key", "type": "string"}, "roleName": {"description": "角色名称", "type": "string"}, "status": {"description": "状态：1->启用,2->禁用", "type": "integer"}}}, "entity.UpdateSysRoleStatusDto": {"type": "object", "properties": {"id": {"description": "ID", "type": "integer"}, "status": {"description": "状态：1->启用,2->禁用", "type": "integer"}}}, "result.Result": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "utils.HTime": {"type": "object", "properties": {"time.Time": {"type": "string"}}}}, "securityDefinitions": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}